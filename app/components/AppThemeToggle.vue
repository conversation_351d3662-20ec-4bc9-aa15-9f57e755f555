<script setup lang="ts">
const { theme, isDark, setTheme } = useTheme();

const cycleTheme = () => {
    if (theme.value === 'system') {
        setTheme('light');
    }
    else if (theme.value === 'light') {
        setTheme('dark');
    }
    else {
        setTheme('system');
    }
};

const getIcon = () => {
    if (theme.value === 'system') {
        return 'i-lucide-monitor';
    }
    return isDark.value ? 'i-lucide-moon' : 'i-lucide-sun';
};
</script>

<template>
    <ClientOnly>
        <UButton
            :icon="getIcon()"
            variant="outline"
            size="md"
            square
            @click="cycleTheme"
        />

        <template #fallback>
            <div class="size-8" />
        </template>
    </ClientOnly>
</template>
