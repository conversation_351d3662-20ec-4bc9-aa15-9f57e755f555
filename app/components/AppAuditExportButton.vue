<script setup lang="ts">
import { CalendarDate, DateFormatter, getLocalTimeZone } from '@internationalized/date';
import type { RadioGroupItem, RadioGroupValue } from '@nuxt/ui';

const exportTypes = ref<RadioGroupItem[]>(['CSV', 'JSON']);
const typeValue = ref<RadioGroupValue>('CSV');

const df = new DateFormatter('en-US', {
    dateStyle: 'medium',
});

const modelValue = shallowRef({
    start: new CalendarDate(2022, 1, 20),
    end: new CalendarDate(2022, 2, 10),
});

const open = ref(false);

defineShortcuts({
    o: () => open.value = !open.value,
});
</script>

<template>
    <UPopover
        v-model:open="open"
        :content="{
            align: 'end',
            side: 'bottom',
            sideOffset: 8,
        }"
    >
        <UButton
            label="Export"
            color="neutral"
            variant="subtle"
            icon="i-lucide-download"
        />

        <template #content>
            <div class="flex flex-col">
                <div class="flex flex-col p-4 gap-2">
                    <div class="font-medium text-sm">
                        Export log
                    </div>
                    <div class="text-xs text-gray-400 font-medium mt-2">
                        Date range
                    </div>
                    <UPopover>
                        <UButton
                            variant="outline"
                            icon="i-lucide-calendar"
                        >
                            <template v-if="modelValue.start">
                                <template v-if="modelValue.end">
                                    {{ df.format(modelValue.start.toDate(getLocalTimeZone())) }} - {{ df.format(modelValue.end.toDate(getLocalTimeZone())) }}
                                </template>

                                <template v-else>
                                    {{ df.format(modelValue.start.toDate(getLocalTimeZone())) }}
                                </template>
                            </template>
                            <template v-else>
                                Pick a date
                            </template>
                        </UButton>

                        <template #content>
                            <UCalendar
                                v-model="modelValue"
                                class="p-2"
                                :number-of-months="2"
                                range
                            />
                        </template>
                    </UPopover>
                    <div class="text-xs text-gray-400 font-medium mt-2">
                        Export type
                    </div>
                    <URadioGroup
                        v-model="typeValue"
                        orientation="horizontal"
                        default-value="CSV"
                        size="xs"
                        :items="exportTypes"
                    />
                </div>
                <div class="h-px bg-gray-200 dark:bg-gray-700" />
                <div class="flex flex-col px-4 pb-4 pt-2 gap-2">
                    <UButton
                        variant="subtle"
                        label="Download"
                        block
                        class="mt-2"
                        icon="i-lucide-file-down"
                    />
                </div>
            </div>
        </template>
    </UPopover>
</template>
