<script setup lang="ts">
import { h, resolveComponent } from 'vue';
import { getPaginationRowModel } from '@tanstack/vue-table';
import type { TableColumn } from '@nuxt/ui';

const searchInput = useTemplateRef('searchInput');
defineShortcuts({
    '/': () => {
        searchInput.value?.inputRef?.focus();
    },
});

const table = useTemplateRef('table');

const UButton = resolveComponent('UButton');
const UBadge = resolveComponent('UBadge');

type PullRequest = {
    approver: string | null;
    approvedAt: string | null;
    id: string;
    repo: string;
    environment: string;
    source: string;
    target: string;
    author: string;
    openedAt: string;
    status: 'unblocked' | 'merged' | 'closed' | 'refreshing';
    title: string;
};

// Generate sample pull request data
const generatePullRequestData = (startId: number, count: number): PullRequest[] => {
    const repos = [
        'frontend-app', 'backend-api', 'mobile-app', 'data-pipeline', 'auth-service',
        'notification-service', 'payment-gateway', 'user-management', 'analytics-dashboard',
        'content-management', 'search-engine', 'recommendation-system',
    ];

    const authors = [
        'john.doe', 'jane.smith', 'mike.johnson', 'sarah.wilson', 'david.brown',
        'emily.davis', 'chris.miller', 'lisa.garcia', 'tom.anderson', 'anna.taylor',
    ];

    const statuses: PullRequest['status'][] = ['unblocked', 'merged', 'closed', 'refreshing'];

    const branches = [
        'feature/user-authentication', 'feature/payment-integration', 'bugfix/memory-leak',
        'feature/dark-mode', 'hotfix/security-patch', 'feature/api-optimization',
        'feature/mobile-responsive', 'bugfix/data-validation', 'feature/search-improvement',
    ];

    const envBranches = [
        'release/qa',
        'release/production',
        'release/ril-production',
    ];

    const branchToEnvMap: Record<string, string> = {
        'release/qa': 'QA',
        'release/production': 'Production',
        'release/ril-production': 'RIL Production',
    };

    return Array.from({ length: count }, (_, i) => {
        const openedAt = new Date(Date.now() - Math.random() * 60 * 24 * 60 * 60 * 1000);
        const status = statuses[Math.floor(Math.random() * statuses.length)] || 'refreshing';
        const target = envBranches[Math.floor(Math.random() * envBranches.length)] || '';

        return {
            approver: ['closed', 'refreshing'].includes(status) ? null : authors[Math.floor(Math.random() * authors.length)] || '',
            approvedAt: ['closed', 'refreshing'].includes(status) ? null : openedAt.toISOString(),
            id: String(startId - i),
            repo: repos[Math.floor(Math.random() * repos.length)] || '',
            source: branches[Math.floor(Math.random() * branches.length)] || '',
            target,
            environment: branchToEnvMap?.[target] || '',
            author: authors[Math.floor(Math.random() * authors.length)] || '',
            openedAt: openedAt.toISOString(),
            status,
            title: `${branches[Math.floor(Math.random() * branches.length)]?.includes('bugfix') ? 'Fix' : 'Add'} ${['user authentication', 'payment processing', 'data validation', 'UI improvements', 'performance optimization'][Math.floor(Math.random() * 5)]}`,
        };
    });
};

// Generate a larger dataset for pagination
const allData = ref<PullRequest[]>([
    ...generatePullRequestData(2500, 500), // Generate more data upfront for pagination
]);

// Global filter state
const globalFilter = ref('');

// Expanded rows state
const expanded = ref<Record<string, boolean>>({});

// Sorting state
const sorting = ref([{ id: 'openedAt', desc: true }]);

// Pagination state
const pagination = ref({
    pageIndex: 0,
    pageSize: 20, // Show 20 items per page
});

const getStatusColor = (status: PullRequest['status']) => {
    switch (status) {
        case 'unblocked': return 'success';
        case 'merged': return 'primary';
        case 'closed': return 'error';
        default: return 'neutral';
    }
};

const columns: TableColumn<PullRequest>[] = [
    {
        id: 'expand',
        cell: ({ row }) =>
            h(UButton, {
                'color': 'neutral',
                'variant': 'ghost',
                'icon': 'i-lucide-chevron-down',
                'loading': row.original.status === 'refreshing',
                'square': true,
                'aria-label': 'Expand',
                'ui': {
                    leadingIcon: [
                        'transition-transform',
                        row.getIsExpanded() ? 'duration-200 rotate-180' : '',
                    ],
                },
                'onClick': () => row.toggleExpanded(),
            }),
        enableSorting: false,
    },
    {
        accessorKey: 'id',
        header: ({ column }) => {
            const isSorted = column.getIsSorted();
            return h(UButton, {
                color: 'neutral',
                variant: 'ghost',
                label: 'PR #',
                icon: isSorted
                    ? isSorted === 'asc'
                        ? 'i-lucide-arrow-up-narrow-wide'
                        : 'i-lucide-arrow-down-wide-narrow'
                    : 'i-lucide-arrow-up-down',
                class: '-mx-2.5 text-xs',
                onClick: () => column.toggleSorting(column.getIsSorted() === 'asc'),
            });
        },
        cell: ({ row }) => h('span', { class: 'text-sm' }, `#${row.getValue('id')}`),
        enableSorting: true,
    },
    {
        accessorKey: 'environment',
        header: ({ column }) => {
            const isSorted = column.getIsSorted();
            return h(UButton, {
                color: 'neutral',
                variant: 'ghost',
                label: 'Environment',
                icon: isSorted
                    ? isSorted === 'asc'
                        ? 'i-lucide-arrow-up-narrow-wide'
                        : 'i-lucide-arrow-down-wide-narrow'
                    : 'i-lucide-arrow-up-down',
                class: '-mx-2.5 text-xs',
                onClick: () => column.toggleSorting(column.getIsSorted() === 'asc'),
            });
        },
        cell: ({ row }) => h('span', { class: 'font-medium' }, row.getValue('environment')),
        enableSorting: true,
    },
    {
        accessorKey: 'repo',
        header: ({ column }) => {
            const isSorted = column.getIsSorted();
            return h(UButton, {
                color: 'neutral',
                variant: 'ghost',
                label: 'Repository',
                icon: isSorted
                    ? isSorted === 'asc'
                        ? 'i-lucide-arrow-up-narrow-wide'
                        : 'i-lucide-arrow-down-wide-narrow'
                    : 'i-lucide-arrow-up-down',
                class: '-mx-2.5 text-xs',
                onClick: () => column.toggleSorting(column.getIsSorted() === 'asc'),
            });
        },
        cell: ({ row }) => h('span', { class: 'font-medium' }, row.getValue('repo')),
        enableSorting: true,
    },
    {
        accessorKey: 'target',
        header: ({ column }) => {
            const isSorted = column.getIsSorted();
            return h(UButton, {
                color: 'neutral',
                variant: 'ghost',
                label: 'Target',
                icon: isSorted
                    ? isSorted === 'asc'
                        ? 'i-lucide-arrow-up-narrow-wide'
                        : 'i-lucide-arrow-down-wide-narrow'
                    : 'i-lucide-arrow-up-down',
                class: '-mx-2.5 text-xs',
                onClick: () => column.toggleSorting(column.getIsSorted() === 'asc'),
            });
        },
        cell: ({ row }) => h('span', { class: 'font-medium' }, row.getValue('target')),
        enableSorting: true,
    },
    {
        accessorKey: 'author',
        header: ({ column }) => {
            const isSorted = column.getIsSorted();
            return h(UButton, {
                color: 'neutral',
                variant: 'ghost',
                label: 'Author',
                icon: isSorted
                    ? isSorted === 'asc'
                        ? 'i-lucide-arrow-up-narrow-wide'
                        : 'i-lucide-arrow-down-wide-narrow'
                    : 'i-lucide-arrow-up-down',
                class: '-mx-2.5 text-xs',
                onClick: () => column.toggleSorting(column.getIsSorted() === 'asc'),
            });
        },
        cell: ({ row }) => h('span', { class: 'font-medium' }, row.getValue('author')),
        enableSorting: true,
    },
    {
        accessorKey: 'openedAt',
        header: ({ column }) => {
            const isSorted = column.getIsSorted();
            return h(UButton, {
                color: 'neutral',
                variant: 'ghost',
                label: 'Opened',
                icon: isSorted
                    ? isSorted === 'asc'
                        ? 'i-lucide-arrow-up-narrow-wide'
                        : 'i-lucide-arrow-down-wide-narrow'
                    : 'i-lucide-arrow-up-down',
                class: '-mx-2.5 text-xs',
                onClick: () => column.toggleSorting(column.getIsSorted() === 'asc'),
            });
        },
        cell: ({ row }) => {
            return new Date(row.getValue('openedAt')).toLocaleString('en-US', {
                day: 'numeric',
                month: 'short',
                hour: '2-digit',
                minute: '2-digit',
                hour12: false,
            });
        },
        enableSorting: true,
    },
    {
        accessorKey: 'status',
        header: ({ column }) => {
            const isSorted = column.getIsSorted();
            return h(UButton, {
                color: 'neutral',
                variant: 'ghost',
                label: 'Status',
                icon: isSorted
                    ? isSorted === 'asc'
                        ? 'i-lucide-arrow-up-narrow-wide'
                        : 'i-lucide-arrow-down-wide-narrow'
                    : 'i-lucide-arrow-up-down',
                class: '-mx-2.5 text-xs',
                onClick: () => column.toggleSorting(column.getIsSorted() === 'asc'),
            });
        },
        cell: ({ row }) => {
            const status = row.getValue('status') as PullRequest['status'];
            return h(UBadge, {
                color: getStatusColor(status),
                variant: 'subtle',
                label: status.charAt(0).toUpperCase() + status.slice(1) + (status === 'refreshing' ? '...' : ''),
            });
        },
        enableSorting: true,
    },
];
</script>

<template>
    <div class="flex flex-col h-full overflow-hidden">
        <div class="p-6 flex-shrink-0">
            <div class="max-w-7xl mx-auto">
                <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
                    <!-- Mobile breadcrumb: two lines -->
                    <div class="lg:hidden">
                        <div class="text-sm font-bold text-gray-400 dark:text-gray-400">
                            Merge Block
                        </div>
                        <div class="text-lg font-bold text-gray-900 dark:text-white">
                            History
                        </div>
                    </div>

                    <h1 class="hidden lg:block text-3xl font-bold text-gray-900 dark:text-white">
                        <span class="text-gray-400 dark:text-gray-400">Merge Block /</span> History
                    </h1>

                    <div class="flex items-center gap-2 flex-row-reverse md:flex-row">
                        <UButton
                            color="neutral"
                            variant="ghost"
                            icon="i-lucide-refresh-cw"
                            size="md"
                            square
                        />
                        <UInput
                            ref="searchInput"
                            v-model="globalFilter"
                            placeholder="Search pull requests..."
                            icon="i-lucide-search"
                            size="md"
                            class="w-full lg:w-auto"
                        >
                            <template #trailing>
                                <UKbd value="/" />
                            </template>
                        </UInput>
                    </div>
                </div>
            </div>
        </div>

        <div class="flex-1 px-6 overflow-hidden">
            <div class="max-w-7xl mx-auto h-[calc(100%-32px)]">
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 h-full flex flex-col">
                    <div class="flex-1 overflow-auto">
                        <UTable
                            ref="table"
                            v-model:expanded="expanded"
                            v-model:sorting="sorting"
                            v-model:global-filter="globalFilter"
                            v-model:pagination="pagination"
                            :data="allData"
                            :columns="columns"
                            :pagination-options="{
                                getPaginationRowModel: getPaginationRowModel(),
                            }"
                            sticky
                            class="flex-1"
                            :ui="{
                                tr: 'data-[expanded=true]:bg-elevated/50',
                                thead: 'bg-gray-100/50 dark:bg-gray-900/50',
                            }"
                        >
                            <template #expanded="{ row }">
                                <div class="p-4 bg-gray-50 dark:bg-gray-900/50">
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm mb-4">
                                        <div v-if="row.original.approver">
                                            <span class="font-medium">Approver:</span>
                                            <span class="ml-2">{{ row.original.approver }}</span>
                                        </div>
                                        <div v-if="row.original.approvedAt">
                                            <span class="font-medium">Approved at:</span>
                                            <span class="ml-2">
                                                {{
                                                    new Date(row.original.approvedAt).toLocaleString('en-US', {
                                                        day: 'numeric',
                                                        month: 'short',
                                                        year: 'numeric',
                                                        hour: '2-digit',
                                                        minute: '2-digit',
                                                        hour12: true,
                                                    })
                                                }}
                                            </span>
                                        </div>
                                        <div class="hidden lg:block">
                                            <span class="font-medium">PR ID:</span>
                                            <span class="ml-2">#{{ row.original.id }}</span>
                                        </div>
                                        <div class="hidden lg:block">
                                            <span class="font-medium">Author:</span>
                                            <span class="ml-2">{{ row.original.author }}</span>
                                        </div>
                                        <div>
                                            <span class="font-medium">Repository:</span>
                                            <span class="ml-2">{{ row.original.repo }}</span>
                                        </div>
                                        <div>
                                            <span class="font-medium">Environment:</span>
                                            <span class="ml-2">{{ row.original.environment }}</span>
                                        </div>
                                        <div class="hidden lg:block">
                                            <span class="font-medium">Source Branch:</span>
                                            <span class="ml-2">{{ row.original.source }}</span>
                                        </div>
                                        <div>
                                            <span class="font-medium">Target Branch:</span>
                                            <span class="ml-2">{{ row.original.target }}</span>
                                        </div>
                                        <div>
                                            <span class="font-medium">Status:</span>
                                            <UBadge
                                                :color="getStatusColor(row.original.status)"
                                                variant="subtle"
                                                :label="row.original.status.charAt(0).toUpperCase() + row.original.status.slice(1)"
                                                class="ml-2"
                                            />
                                        </div>
                                        <div>
                                            <span class="font-medium">Opened:</span>
                                            <span class="ml-2">
                                                {{
                                                    new Date(row.original.openedAt).toLocaleString('en-US', {
                                                        day: 'numeric',
                                                        month: 'short',
                                                        year: 'numeric',
                                                        hour: '2-digit',
                                                        minute: '2-digit',
                                                        hour12: true,
                                                    })
                                                }}
                                            </span>
                                        </div>
                                    </div>
                                    <div>
                                        <span class="font-medium">Title:</span>
                                        <p class="mt-1">
                                            {{ row.original.title }}
                                        </p>
                                    </div>
                                    <div class="mt-4 flex gap-2">
                                        <UButton
                                            v-if="row.original.status === 'unblocked'"
                                            color="error"
                                            trailing-icon="i-lucide-x"
                                            size="md"
                                        >
                                            Block
                                        </UButton>
                                        <UButton
                                            variant="outline"
                                            trailing-icon="i-lucide-github"
                                            size="md"
                                            :to="`https://github.com/sensehawk/${row.original.repo}/pull/${row.original.id}`"
                                        >
                                            View on Github
                                        </UButton>
                                    </div>
                                </div>
                            </template>
                        </UTable>
                    </div>

                    <!-- Pagination controls -->
                    <div class="flex justify-center border-t border-gray-200 dark:border-gray-700 pt-4 pb-4">
                        <UPagination
                            :default-page="(table?.tableApi?.getState().pagination.pageIndex || 0) + 1"
                            :items-per-page="table?.tableApi?.getState().pagination.pageSize"
                            :total="table?.tableApi?.getFilteredRowModel().rows.length"
                            @update:page="(p: number) => table?.tableApi?.setPageIndex(p - 1)"
                        />
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
