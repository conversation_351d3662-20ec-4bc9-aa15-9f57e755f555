<script setup lang="ts">
definePageMeta({
    auth: { unauthenticatedOnly: true, navigateAuthenticatedTo: '/' },
    layout: 'auth',
});

const { signIn } = useAuth();
</script>

<template>
    <div
        class="flex h-full flex-1 items-center justify-center px-4 py-12 sm:px-6 lg:px-8 relative bg-gray-50 dark:bg-gray-900 transition-colors duration-300"
    >
        <AppThemeToggle class="absolute top-4 right-4" />

        <div class="w-full max-w-md space-y-8 select-none">
            <div
                class="md:bg-white md:dark:bg-gray-800 md:shadow-xl md:rounded-2xl md:p-8 md:ring-1 md:ring-gray-200 md:dark:ring-gray-700 p-4"
            >
                <div class="flex flex-col items-center">
                    <UIcon
                        name="i-custom-sensehawk-logo"
                        class="h-12 w-12 animate-fade-in"
                    />
                    <h2 class="mt-6 text-center text-2xl/9 font-semibold">
                        <span
                            class="text-transparent bg-clip-text bg-gradient-to-r from-[#0C82D5] to-[#3BC588] animate-gradient"
                        >
                            SenseHawk
                        </span>
                        <span class="mr-3 text-gray-300 dark:text-gray-600">|</span>
                        <span class="text-gray-500 dark:text-gray-400 bg-gray-50 border border-gray-200 dark:border-gray-500 dark:bg-gray-700 rounded-sm px-4 inline-flex justify-center items-center">
                            Console
                        </span>
                    </h2>
                    <p class="mt-3 text-sm text-gray-500 dark:text-gray-400 hidden md:block">
                        Access your workspace, resources, and internal tools
                    </p>
                </div>

                <div class="mt-8">
                    <UButton
                        icon="logos:google-icon"
                        size="lg"
                        variant="outline"
                        block
                        class="py-3 font-medium"
                        @click="signIn('google')"
                    >
                        Sign in with Google
                    </UButton>
                </div>
            </div>
        </div>
    </div>
</template>
