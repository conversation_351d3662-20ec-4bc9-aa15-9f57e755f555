<script setup lang="ts">
import { h, resolveComponent } from 'vue';
import { getPaginationRowModel } from '@tanstack/vue-table';
import type { TableColumn } from '@nuxt/ui';

const searchInput = useTemplateRef('searchInput');
defineShortcuts({
    '/': () => {
        searchInput.value?.inputRef?.focus();
    },
});

const table = useTemplateRef('table');

const UButton = resolveComponent('UButton');

type Payment = {
    date: string;
    actor: string;
    action: string;
};

// Generate more data for infinite scroll demonstration
const generateMoreData = (count: number): Payment[] => {
    const actors = [
        '<EMAIL>', '<EMAIL>', '<EMAIL>',
        '<EMAIL>', '<EMAIL>', '<EMAIL>',
        '<EMAIL>', '<EMAIL>', '<EMAIL>',
        '<EMAIL>', '<EMAIL>', '<EMAIL>',
        '<EMAIL>', '<EMAIL>', '<EMAIL>',
        '<EMAIL>', '<EMAIL>', '<EMAIL>',
        '<EMAIL>', '<EMAIL>',
    ];

    const actions = [
        'created a new user', 'updated user information', 'deleted a user',
        'created a new role', 'updated role permissions', 'deleted a role',
        'assigned a role to a user', 'revoked a role from a user',
    ];

    return Array.from({ length: count }, () => ({
        date: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
        actor: actors[Math.floor(Math.random() * actors.length)] || '<EMAIL>',
        action: actions[Math.floor(Math.random() * actions.length)] || 'Unknown action',
    }));
};

// Generate a larger dataset for pagination
const allData = ref<Payment[]>([
    ...generateMoreData(500), // Generate more data upfront for pagination
]);

// Global filter state
const globalFilter = ref('');

// Expanded rows state
const expanded = ref<Record<string, boolean>>({});

// Sorting state
const sorting = ref([{ id: 'date', desc: true }]);

// Pagination state
const pagination = ref({
    pageIndex: 0,
    pageSize: 20, // Show 20 items per page
});
const columns: TableColumn<Payment>[] = [
    {
        id: 'expand',
        cell: ({ row }) =>
            h(UButton, {
                'color': 'neutral',
                'variant': 'ghost',
                'icon': 'i-lucide-chevron-down',
                'square': true,
                'aria-label': 'Expand',
                'ui': {
                    leadingIcon: [
                        'transition-transform',
                        row.getIsExpanded() ? 'duration-200 rotate-180' : '',
                    ],
                },
                'onClick': () => row.toggleExpanded(),
            }),
        enableSorting: false,
    },
    {
        accessorKey: 'date',
        header: ({ column }) => {
            const isSorted = column.getIsSorted();
            return h(UButton, {
                color: 'neutral',
                variant: 'ghost',
                label: 'Date',
                icon: isSorted
                    ? isSorted === 'asc'
                        ? 'i-lucide-arrow-up-narrow-wide'
                        : 'i-lucide-arrow-down-wide-narrow'
                    : 'i-lucide-arrow-up-down',
                class: '-mx-2.5 text-xs',
                onClick: () => column.toggleSorting(column.getIsSorted() === 'asc'),
            });
        },
        cell: ({ row }) => {
            return new Date(row.getValue('date')).toLocaleString('en-US', {
                day: 'numeric',
                month: 'short',
                year: 'numeric',
                hour: '2-digit',
                minute: '2-digit',
                hour12: true,
            });
        },
        enableSorting: true,
    },
    {
        accessorKey: 'actor',
        header: ({ column }) => {
            const isSorted = column.getIsSorted();
            return h(UButton, {
                color: 'neutral',
                variant: 'ghost',
                label: 'Actor',
                icon: isSorted
                    ? isSorted === 'asc'
                        ? 'i-lucide-arrow-up-narrow-wide'
                        : 'i-lucide-arrow-down-wide-narrow'
                    : 'i-lucide-arrow-up-down',
                class: '-mx-2.5 text-xs',
                onClick: () => column.toggleSorting(column.getIsSorted() === 'asc'),
            });
        },
        enableSorting: true,
    },
    {
        accessorKey: 'action',
        header: () => h('span', { class: 'text-xs' }, 'Action'),
        enableSorting: true,
    },
];
</script>

<template>
    <div class="flex flex-col h-full overflow-hidden">
        <div class="p-6 flex-shrink-0">
            <div class="max-w-7xl mx-auto">
                <div class="flex flex-col items-start justify-between">
                    <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-4">
                        Audit Log
                    </h1>

                    <div class="flex w-full gap-2">
                        <UInput
                            ref="searchInput"
                            v-model="globalFilter"
                            placeholder="Search..."
                            icon="i-lucide-search"
                            size="md"
                            class="w-full"
                        >
                            <template #trailing>
                                <UKbd value="/" />
                            </template>
                        </UInput>
                        <AppAuditExportButton />
                    </div>
                </div>
            </div>
        </div>

        <div class="flex-1 px-6 overflow-hidden">
            <div class="max-w-7xl mx-auto h-[calc(100%-32px)]">
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 h-full flex flex-col">
                    <div class="flex-1 overflow-auto">
                        <UTable
                            ref="table"
                            v-model:expanded="expanded"
                            v-model:sorting="sorting"
                            v-model:global-filter="globalFilter"
                            v-model:pagination="pagination"
                            :data="allData"
                            :columns="columns"
                            :pagination-options="{
                                getPaginationRowModel: getPaginationRowModel(),
                            }"
                            sticky
                            class="flex-1"
                            :ui="{
                                tr: 'data-[expanded=true]:bg-elevated/50',
                                thead: 'bg-gray-100/50 dark:bg-gray-900/50',
                            }"
                        >
                            <template #expanded="{ row }">
                                <div class="p-4 bg-gray-50 dark:bg-gray-900/50">
                                    <h4 class="font-semibold mb-2">
                                        Payment Details
                                    </h4>
                                    <div class="grid grid-cols-2 gap-4 text-sm">
                                        <div>
                                            <span class="font-medium">Date:</span>
                                            <span class="ml-2">{{ new Date(row.original.date).toLocaleString() }}</span>
                                        </div>
                                        <div>
                                            <span class="font-medium">Actor:</span>
                                            <span class="ml-2">{{ row.original.actor }}</span>
                                        </div>
                                        <div>
                                            <span class="font-medium">Action:</span>
                                            <span class="ml-2">{{ row.original.action }}</span>
                                        </div>
                                    </div>
                                    <div class="mt-4">
                                        <pre class="text-xs bg-gray-100 dark:bg-gray-800 p-2 rounded">{{ JSON.stringify(row.original, null, 2) }}</pre>
                                    </div>
                                </div>
                            </template>
                        </UTable>
                    </div>

                    <!-- Pagination controls -->
                    <div class="flex justify-center border-t border-gray-200 dark:border-gray-700 pt-4 pb-4">
                        <UPagination
                            :default-page="(table?.tableApi?.getState().pagination.pageIndex || 0) + 1"
                            :items-per-page="table?.tableApi?.getState().pagination.pageSize"
                            :total="table?.tableApi?.getFilteredRowModel().rows.length"
                            @update:page="(p: number) => table?.tableApi?.setPageIndex(p - 1)"
                        />
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
