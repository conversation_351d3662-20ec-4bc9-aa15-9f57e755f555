<script setup lang="ts">
import { h, resolveComponent } from 'vue';
import { getPaginationRowModel } from '@tanstack/vue-table';
import type { TableColumn } from '@nuxt/ui';

const { data } = useAuth();
const userName = computed(() => data.value?.user?.name);

const route = useRoute();
const router = useRouter();

// Active tab state - initialize from URL query parameter
const activeTab = ref(route.query.tab as string || 'overview');

// Watch for route changes to update active tab
watch(() => route.query.tab, (newTab) => {
    if (newTab && typeof newTab === 'string') {
        activeTab.value = newTab;
    }
});

// Function to change tab and update URL
const changeTab = (tabKey: string) => {
    activeTab.value = tabKey;
    router.push({ query: { tab: tabKey } });
};

// Audit log functionality (moved from audit-log.vue)
const searchInput = useTemplateRef('searchInput');
defineShortcuts({
    '/': () => {
        if (activeTab.value === 'audit-log') {
            searchInput.value?.inputRef?.focus();
        }
    },
});

const table = useTemplateRef('table');
const UButton = resolveComponent('UButton');

type AuditEntry = {
    date: string;
    actor: string;
    action: string;
};

const generateAuditData = (count: number): AuditEntry[] => {
    const actors = [
        '<EMAIL>', '<EMAIL>', '<EMAIL>',
        '<EMAIL>', '<EMAIL>', '<EMAIL>',
    ];

    const actions = [
        'created a new user', 'updated user information', 'deleted a user',
        'created a new role', 'updated role permissions', 'deleted a role',
        'assigned a role to a user', 'revoked a role from a user',
        'modified system settings', 'exported audit log',
    ];

    return Array.from({ length: count }, () => ({
        date: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
        actor: actors[Math.floor(Math.random() * actors.length)] || '<EMAIL>',
        action: actions[Math.floor(Math.random() * actions.length)] || 'Unknown action',
    }));
};

// Generate audit log data
const allAuditData = ref<AuditEntry[]>([
    ...generateAuditData(500),
]);

// Global filter state for audit log
const globalFilter = ref('');

// Expanded rows state for audit log
const expanded = ref<Record<string, boolean>>({});

// Sorting state for audit log
const sorting = ref([{ id: 'date', desc: true }]);

// Pagination state for audit log
const pagination = ref({
    pageIndex: 0,
    pageSize: 20,
});

// Audit log table columns
const auditColumns: TableColumn<AuditEntry>[] = [
    {
        id: 'expand',
        cell: ({ row }) =>
            h(UButton, {
                'color': 'neutral',
                'variant': 'ghost',
                'icon': 'i-lucide-chevron-down',
                'square': true,
                'aria-label': 'Expand',
                'ui': {
                    leadingIcon: [
                        'transition-transform',
                        row.getIsExpanded() ? 'duration-200 rotate-180' : '',
                    ],
                },
                'onClick': () => row.toggleExpanded(),
            }),
        enableSorting: false,
    },
    {
        accessorKey: 'date',
        header: ({ column }) => {
            const isSorted = column.getIsSorted();
            return h(UButton, {
                color: 'neutral',
                variant: 'ghost',
                label: 'Date',
                icon: isSorted
                    ? isSorted === 'asc'
                        ? 'i-lucide-arrow-up-narrow-wide'
                        : 'i-lucide-arrow-down-wide-narrow'
                    : 'i-lucide-arrow-up-down',
                class: '-mx-2.5 text-xs',
                onClick: () => column.toggleSorting(column.getIsSorted() === 'asc'),
            });
        },
        cell: ({ row }) => {
            const date = new Date(row.original.date);
            return h('div', { class: 'text-xs' }, [
                h('div', { class: 'font-medium' }, date.toLocaleDateString()),
                h('div', { class: 'text-gray-500 dark:text-gray-400' }, date.toLocaleTimeString()),
            ]);
        },
        enableSorting: true,
    },
    {
        accessorKey: 'actor',
        header: ({ column }) => {
            const isSorted = column.getIsSorted();
            return h(UButton, {
                color: 'neutral',
                variant: 'ghost',
                label: 'Actor',
                icon: isSorted
                    ? isSorted === 'asc'
                        ? 'i-lucide-arrow-up-narrow-wide'
                        : 'i-lucide-arrow-down-wide-narrow'
                    : 'i-lucide-arrow-up-down',
                class: '-mx-2.5 text-xs',
                onClick: () => column.toggleSorting(column.getIsSorted() === 'asc'),
            });
        },
        enableSorting: true,
    },
    {
        accessorKey: 'action',
        header: () => h('span', { class: 'text-xs' }, 'Action'),
        enableSorting: true,
    },
];

// Tab configuration
const tabs = [
    {
        key: 'overview',
        label: 'Overview',
        icon: 'i-lucide-layout-dashboard',
    },
    {
        key: 'users',
        label: 'Users',
        icon: 'i-lucide-users',
    },
    {
        key: 'roles',
        label: 'Roles & Permissions',
        icon: 'i-lucide-shield-check',
    },
    {
        key: 'audit-log',
        label: 'Audit Log',
        icon: 'i-lucide-logs',
    },
    {
        key: 'system',
        label: 'System Settings',
        icon: 'i-lucide-settings',
    },
];
</script>

<template>
    <div class="flex flex-col h-full overflow-hidden">
        <div class="p-6 flex-shrink-0">
            <div class="max-w-7xl mx-auto">
                <div class="flex flex-col items-start justify-between mb-6">
                    <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-4">
                        Administration
                    </h1>
                    <p class="text-gray-600 dark:text-gray-400">
                        Manage users, roles, system settings, and monitor activity
                    </p>
                </div>

                <!-- Tab Navigation -->
                <div class="border-b border-gray-200 dark:border-gray-700">
                    <nav class="-mb-px flex space-x-8">
                        <button
                            v-for="tab in tabs"
                            :key="tab.key"
                            :class="[
                                'flex items-center py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200',
                                activeTab === tab.key
                                    ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300',
                            ]"
                            @click="changeTab(tab.key)"
                        >
                            <UIcon
                                :name="tab.icon"
                                class="h-4 w-4 mr-2"
                            />
                            {{ tab.label }}
                        </button>
                    </nav>
                </div>
            </div>
        </div>

        <!-- Tab Content -->
        <div class="flex-1 px-6 overflow-hidden">
            <div class="max-w-7xl mx-auto h-full">
                <!-- Overview Tab -->
                <div
                    v-if="activeTab === 'overview'"
                    class="space-y-6"
                >
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                        <!-- Stats Cards -->
                        <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 p-6">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <UIcon
                                        name="i-lucide-users"
                                        class="h-8 w-8 text-blue-600 dark:text-blue-400"
                                    />
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">
                                        Total Users
                                    </p>
                                    <p class="text-2xl font-semibold text-gray-900 dark:text-white">
                                        1,247
                                    </p>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 p-6">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <UIcon
                                        name="i-lucide-shield-check"
                                        class="h-8 w-8 text-green-600 dark:text-green-400"
                                    />
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">
                                        Active Roles
                                    </p>
                                    <p class="text-2xl font-semibold text-gray-900 dark:text-white">
                                        12
                                    </p>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 p-6">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <UIcon
                                        name="i-lucide-activity"
                                        class="h-8 w-8 text-yellow-600 dark:text-yellow-400"
                                    />
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">
                                        Today's Activity
                                    </p>
                                    <p class="text-2xl font-semibold text-gray-900 dark:text-white">
                                        89
                                    </p>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 p-6">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <UIcon
                                        name="i-lucide-server"
                                        class="h-8 w-8 text-purple-600 dark:text-purple-400"
                                    />
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">
                                        System Health
                                    </p>
                                    <p class="text-2xl font-semibold text-green-600 dark:text-green-400">
                                        Healthy
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Recent Activity -->
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700">
                        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                                Recent Activity
                            </h3>
                        </div>
                        <div class="p-6">
                            <div class="space-y-4">
                                <div
                                    v-for="(entry, index) in allAuditData.slice(0, 5)"
                                    :key="index"
                                    class="flex items-center space-x-4"
                                >
                                    <div class="flex-shrink-0">
                                        <div class="h-8 w-8 rounded-full bg-blue-100 dark:bg-blue-900/50 flex items-center justify-center">
                                            <UIcon
                                                name="i-lucide-user"
                                                class="h-4 w-4 text-blue-600 dark:text-blue-400"
                                            />
                                        </div>
                                    </div>
                                    <div class="flex-1 min-w-0">
                                        <p class="text-sm font-medium text-gray-900 dark:text-white">
                                            {{ entry.actor }}
                                        </p>
                                        <p class="text-sm text-gray-500 dark:text-gray-400">
                                            {{ entry.action }}
                                        </p>
                                    </div>
                                    <div class="flex-shrink-0 text-sm text-gray-500 dark:text-gray-400">
                                        {{ new Date(entry.date).toLocaleString() }}
                                    </div>
                                </div>
                            </div>
                            <div class="mt-6">
                                <UButton
                                    variant="outline"
                                    size="sm"
                                    @click="changeTab('audit-log')"
                                >
                                    View Full Audit Log
                                </UButton>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Users Tab -->
                <div
                    v-else-if="activeTab === 'users'"
                    class="space-y-6"
                >
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700">
                        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                            <div class="flex items-center justify-between">
                                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                                    User Management
                                </h3>
                                <UButton
                                    icon="i-lucide-plus"
                                    size="sm"
                                >
                                    Add User
                                </UButton>
                            </div>
                        </div>
                        <div class="p-6">
                            <p class="text-gray-600 dark:text-gray-400 mb-4">
                                Manage user accounts, permissions, and access levels.
                            </p>
                            <div class="text-center py-12">
                                <UIcon
                                    name="i-lucide-users"
                                    class="h-12 w-12 text-gray-400 mx-auto mb-4"
                                />
                                <p class="text-gray-500 dark:text-gray-400">
                                    User management interface coming soon
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Roles Tab -->
                <div
                    v-else-if="activeTab === 'roles'"
                    class="space-y-6"
                >
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700">
                        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                            <div class="flex items-center justify-between">
                                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                                    Roles & Permissions
                                </h3>
                                <UButton
                                    icon="i-lucide-plus"
                                    size="sm"
                                >
                                    Create Role
                                </UButton>
                            </div>
                        </div>
                        <div class="p-6">
                            <p class="text-gray-600 dark:text-gray-400 mb-4">
                                Define roles and manage permissions for different user groups.
                            </p>
                            <div class="text-center py-12">
                                <UIcon
                                    name="i-lucide-shield-check"
                                    class="h-12 w-12 text-gray-400 mx-auto mb-4"
                                />
                                <p class="text-gray-500 dark:text-gray-400">
                                    Role management interface coming soon
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Audit Log Tab -->
                <div
                    v-else-if="activeTab === 'audit-log'"
                    class="h-full flex flex-col"
                >
                    <div class="mb-4">
                        <div class="flex w-full gap-2">
                            <UInput
                                ref="searchInput"
                                v-model="globalFilter"
                                placeholder="Search audit log..."
                                icon="i-lucide-search"
                                size="md"
                                class="w-full"
                            >
                                <template #trailing>
                                    <UKbd value="/" />
                                </template>
                            </UInput>
                            <AppAuditExportButton />
                        </div>
                    </div>

                    <div class="flex-1 overflow-hidden">
                        <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 h-full flex flex-col">
                            <div class="flex-1 overflow-auto">
                                <UTable
                                    ref="table"
                                    v-model:expanded="expanded"
                                    v-model:sorting="sorting"
                                    v-model:global-filter="globalFilter"
                                    v-model:pagination="pagination"
                                    :data="allAuditData"
                                    :columns="auditColumns"
                                    :pagination-options="{
                                        getPaginationRowModel: getPaginationRowModel(),
                                    }"
                                    sticky
                                    class="flex-1"
                                    :ui="{
                                        tr: 'data-[expanded=true]:bg-elevated/50',
                                        thead: 'bg-gray-100/50 dark:bg-gray-900/50',
                                    }"
                                >
                                    <template #expanded="{ row }">
                                        <div class="p-4 bg-gray-50 dark:bg-gray-900/50">
                                            <h4 class="font-semibold mb-2">
                                                Activity Details
                                            </h4>
                                            <div class="grid grid-cols-2 gap-4 text-sm">
                                                <div>
                                                    <span class="font-medium">Date:</span>
                                                    <span class="ml-2">{{ new Date(row.original.date).toLocaleString() }}</span>
                                                </div>
                                                <div>
                                                    <span class="font-medium">Actor:</span>
                                                    <span class="ml-2">{{ row.original.actor }}</span>
                                                </div>
                                                <div>
                                                    <span class="font-medium">Action:</span>
                                                    <span class="ml-2">{{ row.original.action }}</span>
                                                </div>
                                            </div>
                                            <div class="mt-4">
                                                <pre class="text-xs bg-gray-100 dark:bg-gray-800 p-2 rounded">{{ JSON.stringify(row.original, null, 2) }}</pre>
                                            </div>
                                        </div>
                                    </template>
                                </UTable>
                            </div>

                            <!-- Pagination controls -->
                            <div class="flex justify-center border-t border-gray-200 dark:border-gray-700 pt-4 pb-4">
                                <UPagination
                                    :default-page="(table?.tableApi?.getState().pagination.pageIndex || 0) + 1"
                                    :items-per-page="table?.tableApi?.getState().pagination.pageSize"
                                    :total="table?.tableApi?.getFilteredRowModel().rows.length"
                                    @update:page="(p: number) => table?.tableApi?.setPageIndex(p - 1)"
                                />
                            </div>
                        </div>
                    </div>
                </div>

                <!-- System Settings Tab -->
                <div
                    v-else-if="activeTab === 'system'"
                    class="space-y-6"
                >
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700">
                        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                                System Settings
                            </h3>
                        </div>
                        <div class="p-6">
                            <p class="text-gray-600 dark:text-gray-400 mb-4">
                                Configure system-wide settings and preferences.
                            </p>
                            <div class="text-center py-12">
                                <UIcon
                                    name="i-lucide-settings"
                                    class="h-12 w-12 text-gray-400 mx-auto mb-4"
                                />
                                <p class="text-gray-500 dark:text-gray-400">
                                    System settings interface coming soon
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
