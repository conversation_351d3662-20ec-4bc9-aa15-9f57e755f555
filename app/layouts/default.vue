<script setup lang="ts">
const sidebarRef = ref();
</script>

<template>
    <div class="flex flex-col h-screen overflow-hidden">
        <div
            class="h-16 w-full flex items-center justify-between gap-4 px-4 border-b border-gray-200 dark:border-gray-800 bg-gray-50 dark:bg-gray-900"
        >
            <div class="flex items-center gap-4">
                <UButton
                    icon="i-lucide-menu"
                    variant="ghost"
                    size="md"
                    square
                    class="lg:hidden"
                    @click="sidebarRef?.toggleSidebar()"
                />
                <div
                    class="flex items-center gap-4"
                    @click="$router.push('/')"
                >
                    <UIcon
                        name="i-custom-sensehawk-logo"
                        class="h-8 w-8"
                    />
                    <span class="text-gray-500 dark:text-gray-400 inline-flex justify-center items-center text-2xl font-semibold">
                        Console
                    </span>
                </div>
            </div>
            <div class="flex items-center gap-2">
                <AppUserMenu />
            </div>
        </div>

        <div class="flex flex-1 overflow-hidden">
            <AppSidebar ref="sidebarRef" />

            <div class="flex-1 overflow-auto">
                <slot />
            </div>
        </div>
    </div>
</template>
