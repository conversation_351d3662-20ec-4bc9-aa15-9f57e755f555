// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
    modules: [
        '@nuxt/eslint',
        '@nuxt/image',
        '@nuxt/scripts',
        '@nuxt/test-utils',
        '@nuxt/ui',
        '@sidebase/nuxt-auth',
        '@vueuse/nuxt',
    ],
    ssr: false,
    devtools: { enabled: true },
    css: ['~/assets/css/main.css'],
    colorMode: {
        preference: 'system',
        fallback: 'light',
        storage: 'localStorage',
        storageKey: 'color-mode',
    },
    ui: {
        theme: {
            defaultVariants: {
                // color: 'neutral',
                size: 'sm',
            },
        },
    },
    compatibilityDate: '2025-07-15',
    auth: {
        provider: {
            type: 'authjs',
        },
        globalAppMiddleware: true,
    },
    eslint: {
        config: {
            stylistic: {
                semi: true,
                indent: 4,
            },
        },
    },
    icon: {
        customCollections: [{
            prefix: 'custom',
            dir: './app/assets/icons',
        }],
    },
});
